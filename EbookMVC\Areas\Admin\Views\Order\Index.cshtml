@model IEnumerable<EbookMVC.Models.Order>
@using EbookMVC.Models

@{
    ViewData["Title"] = "Quản lý đơn hàng";
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin/Home">Home</a></li>
                    <li class="breadcrumb-item active">Quản lý đơn hàng</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Danh sách đơn hàng</h3>
                        <div class="card-tools">
                            <a asp-action="Statistics" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-bar"></i> Thống kê
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filter Form -->
                        <div class="mb-3">
                            <form asp-action="Index" method="get" class="row">
                                <div class="col-md-3">
                                    <input type="text" name="searchKeyword" class="form-control" placeholder="Tìm theo mã đơn hàng, tên khách hàng..." value="@ViewBag.SearchKeyword" />
                                </div>
                                <div class="col-md-2">
                                    <select name="status" class="form-control">
                                        <option value="">-- Tất cả trạng thái --</option>
                                        @foreach (EbookMVC.Models.OrderStatus status in ViewBag.OrderStatuses)
                                        {
                                            <option value="@status" selected="@(status.ToString() == ViewBag.Status?.ToString())">@status.GetDisplayName()</option>
                                        }
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="date" name="startDate" class="form-control" value="@ViewBag.StartDate" />
                                </div>
                                <div class="col-md-2">
                                    <input type="date" name="endDate" class="form-control" value="@ViewBag.EndDate" />
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> Làm mới
                                    </a>
                                </div>
                            </form>
                        </div>

                        <!-- Success/Error Messages -->
                        @if (TempData["SuccessMessage"] != null)
                        {
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle"></i> @TempData["SuccessMessage"]
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        }

                        @if (TempData["ErrorMessage"] != null)
                        {
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> @TempData["ErrorMessage"]
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        }

                        <!-- Orders Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Mã đơn hàng</th>
                                        <th>Khách hàng</th>
                                        <th>Ngày đặt</th>
                                        <th>Trạng thái</th>
                                        <th>Thanh toán</th>
                                        <th>Tổng tiền</th>
                                        <th style="width: 150px">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model?.Any() == true)
                                    {
                                        @foreach (var order in Model)
                                        {
                                            <tr>
                                                <td>
                                                    <strong>@order.OrderNumber</strong>
                                                </td>
                                                <td>
                                                    @order.ShippingName
                                                    <br><small class="text-muted">@order.ShippingEmail</small>
                                                </td>
                                                <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                                <td>
                                                    <span class="badge @order.StatusBadgeClass">@order.StatusDisplayName</span>
                                                </td>
                                                <td class="text-center">
                                                    @if (order.IsPaid)
                                                    {
                                                        <span class="badge badge-success">
                                                            <i class="fas fa-check"></i> Đã thanh toán
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge badge-warning">
                                                            <i class="fas fa-clock"></i> Chưa thanh toán
                                                        </span>
                                                    }
                                                </td>
                                                <td class="text-right">
                                                    <strong class="text-success">@order.GrandTotal.ToString("N0") VNĐ</strong>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="/Admin/Order/Details/@order.Id" class="btn btn-info btn-sm" title="Xem chi tiết">
                                                            <i class="fas fa-eye"></i> Chi tiết
                                                        </a>
                                                        @if (order.Status == EbookMVC.Models.OrderStatus.Cancelled)
                                                        {
                                                            <form asp-action="Delete" asp-route-id="@order.Id" method="post" style="display: inline;"
                                                                  onsubmit="return confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')">
                                                                <button type="submit" class="btn btn-danger btn-sm" title="Xóa">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    }
                                    else
                                    {
                                        <tr>
                                            <td colspan="7" class="text-center">Không có đơn hàng nào</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (Model?.Any() == true)
        {
            <!-- Order Statistics -->
            <div class="row">
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-info"><i class="fas fa-shopping-bag"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Tổng đơn hàng</span>
                            <span class="info-box-number">@Model.Count()</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-success"><i class="fas fa-check"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Đã hoàn thành</span>
                            <span class="info-box-number">@Model.Count(o => o.Status == EbookMVC.Models.OrderStatus.Completed || o.Status == EbookMVC.Models.OrderStatus.Delivered)</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Đang xử lý</span>
                            <span class="info-box-number">@Model.Count(o => o.Status == EbookMVC.Models.OrderStatus.Pending || o.Status == EbookMVC.Models.OrderStatus.Processing)</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-box">
                        <span class="info-box-icon bg-primary"><i class="fas fa-money-bill"></i></span>
                        <div class="info-box-content">
                            <span class="info-box-text">Tổng doanh thu</span>
                            <span class="info-box-number">@Model.Where(o => o.Status != EbookMVC.Models.OrderStatus.Cancelled).Sum(o => o.GrandTotal).ToString("N0") VNĐ</span>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</section>
