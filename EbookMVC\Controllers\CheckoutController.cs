using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using EbookMVC.Models;
using EbookMVC.Repository;
using System.Security.Claims;

namespace EbookMVC.Controllers
{
    [Authorize]
    public class CheckoutController : Controller
    {
        private readonly ICartRepository _cartRepository;
        private readonly IOrderRepository _orderRepository;
        private readonly IVoucherRepository _voucherRepository;
        private readonly IUserLogRepository _userLogRepository;
        private readonly UserManager<ApplicationUser> _userManager;

        public CheckoutController(
            ICartRepository cartRepository,
            IOrderRepository orderRepository,
            IVoucherRepository voucherRepository,
            IUserLogRepository userLogRepository,
            UserManager<ApplicationUser> userManager)
        {
            _cartRepository = cartRepository;
            _orderRepository = orderRepository;
            _voucherRepository = voucherRepository;
            _userLogRepository = userLogRepository;
            _userManager = userManager;
        }

        // GET: Checkout
        public async Task<IActionResult> Index()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return RedirectToAction("Login", "Account");
            }

            var cart = _cartRepository.GetCartByUserId(userId);
            if (cart == null || !cart.CartItems.Any())
            {
                TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống!";
                return RedirectToAction("Index", "Cart");
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var checkoutViewModel = new CheckoutViewModel
            {
                Cart = cart,
                User = user,
                ShippingAddress = new ShippingAddress
                {
                    FullName = user.FullName ?? "",
                    Email = user.Email ?? "",
                    PhoneNumber = user.PhoneNumber ?? "",
                    Address = user.Address ?? "",
                    City = user.City ?? "",
                    PostalCode = user.PostalCode ?? ""
                },
                PaymentMethod = "COD", // Default to Cash on Delivery
                ShippingFee = CalculateShippingFee(cart.TotalAmount),
                Tax = CalculateTax(cart.TotalAmount)
            };

            return View(checkoutViewModel);
        }

        // POST: Checkout/ApplyVoucher
        [HttpPost]
        public IActionResult ApplyVoucher(string voucherCode)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Vui lòng đăng nhập!" });
            }

            var cart = _cartRepository.GetCartByUserId(userId);
            if (cart == null || !cart.CartItems.Any())
            {
                return Json(new { success = false, message = "Giỏ hàng trống!" });
            }

            if (string.IsNullOrEmpty(voucherCode))
            {
                return Json(new { success = false, message = "Vui lòng nhập mã voucher!" });
            }

            var voucher = _voucherRepository.ValidateVoucher(voucherCode, cart.TotalAmount);
            if (voucher == null)
            {
                // Get detailed error message
                var existingVoucher = _voucherRepository.GetByCode(voucherCode);
                string errorMessage = "Mã voucher không hợp lệ!";
                
                if (existingVoucher == null)
                {
                    errorMessage = "Mã voucher không tồn tại!";
                }
                else if (!existingVoucher.IsActive)
                {
                    errorMessage = "Mã voucher đã bị vô hiệu hóa!";
                }
                else if (DateTime.Now < existingVoucher.StartDate)
                {
                    errorMessage = $"Mã voucher chưa có hiệu lực! Có hiệu lực từ {existingVoucher.StartDate:dd/MM/yyyy HH:mm}";
                }
                else if (DateTime.Now > existingVoucher.EndDate)
                {
                    errorMessage = "Mã voucher đã hết hạn!";
                }
                else if (existingVoucher.Quantity.HasValue && existingVoucher.RemainingQuantity <= 0)
                {
                    errorMessage = "Mã voucher đã hết lượt sử dụng!";
                }
                else if (cart.TotalAmount < existingVoucher.MinOrderValue)
                {
                    errorMessage = $"Đơn hàng tối thiểu {existingVoucher.MinOrderValue:N0} VNĐ để sử dụng voucher này!";
                }

                return Json(new { success = false, message = errorMessage });
            }

            var discount = voucher.CalculateDiscount(cart.TotalAmount);
            
            // Apply voucher to cart
            _cartRepository.ApplyVoucher(userId, voucher.Id, voucher.Code, discount);

            // Calculate new totals
            var shippingFee = CalculateShippingFee(cart.TotalAmount);
            var tax = CalculateTax(cart.TotalAmount);
            var finalAmount = cart.TotalAmount + shippingFee + tax - discount;

            return Json(new
            {
                success = true,
                message = "Áp dụng mã voucher thành công!",
                data = new
                {
                    voucherCode = voucher.Code,
                    voucherName = voucher.Name,
                    discount = discount,
                    orderValue = cart.TotalAmount,
                    shippingFee = shippingFee,
                    tax = tax,
                    finalAmount = finalAmount
                }
            });
        }

        // POST: Checkout/RemoveVoucher
        [HttpPost]
        public IActionResult RemoveVoucher()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Vui lòng đăng nhập!" });
            }

            _cartRepository.RemoveVoucher(userId);

            var cart = _cartRepository.GetCartByUserId(userId);
            var shippingFee = CalculateShippingFee(cart.TotalAmount);
            var tax = CalculateTax(cart.TotalAmount);
            var finalAmount = cart.TotalAmount + shippingFee + tax;

            return Json(new
            {
                success = true,
                message = "Đã hủy mã voucher!",
                data = new
                {
                    orderValue = cart.TotalAmount,
                    shippingFee = shippingFee,
                    tax = tax,
                    finalAmount = finalAmount
                }
            });
        }

        // POST: Checkout/PlaceOrder
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PlaceOrder(CheckoutViewModel model)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return RedirectToAction("Login", "Account");
            }

            var cart = _cartRepository.GetCartByUserId(userId);
            if (cart == null || !cart.CartItems.Any())
            {
                TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống!";
                return RedirectToAction("Index", "Cart");
            }

            if (!ModelState.IsValid)
            {
                model.Cart = cart;
                model.User = await _userManager.FindByIdAsync(userId);
                model.ShippingFee = CalculateShippingFee(cart.TotalAmount);
                model.Tax = CalculateTax(cart.TotalAmount);
                return View("Index", model);
            }

            try
            {
                // Create order
                var order = new Order
                {
                    UserId = userId,
                    OrderDate = DateTime.Now,
                    TotalAmount = cart.TotalAmount,
                    ShippingFee = model.ShippingFee,
                    Tax = model.Tax,
                    VoucherCode = cart.VoucherCode,
                    VoucherId = cart.VoucherId,
                    VoucherDiscount = cart.VoucherDiscount,
                    Status = OrderStatus.Pending,
                    PaymentMethod = model.PaymentMethod,
                    ShippingName = model.ShippingAddress.FullName,
                    ShippingAddress = $"{model.ShippingAddress.Address}, {model.ShippingAddress.City}, {model.ShippingAddress.PostalCode}",
                    ShippingPhone = model.ShippingAddress.PhoneNumber,
                    ShippingEmail = model.ShippingAddress.Email,
                    Notes = model.Notes,
                    UpdatedDate = DateTime.Now
                };

                // Add order items
                foreach (var cartItem in cart.CartItems)
                {
                    order.OrderItems.Add(new OrderItem
                    {
                        ProductId = cartItem.ProductId,
                        ProductName = cartItem.Product.Name,
                        UnitPrice = cartItem.UnitPrice,
                        Quantity = cartItem.Quantity,
                        OriginalPrice = cartItem.Product.Price,
                        Discount = 0
                    });
                }

                _orderRepository.Add(order);

                // Use voucher if applied
                if (cart.VoucherId.HasValue)
                {
                    _voucherRepository.UseVoucher(cart.VoucherId.Value);
                }

                // Clear cart
                _cartRepository.ClearCart(cart.Id);

                // Log order creation
                _userLogRepository.Add(new UserLog
                {
                    Action = "CREATE_ORDER",
                    EntityName = "Order",
                    EntityId = order.Id,
                    Description = $"Tạo đơn hàng mới với tổng tiền {order.GrandTotal:N0} VNĐ" + 
                                 (cart.HasVoucher ? $" (áp dụng voucher {cart.VoucherCode})" : ""),
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Guest",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = $"Đặt hàng thành công! Mã đơn hàng: #{order.Id:D6}";
                TempData["OrderId"] = order.Id;
                TempData["OrderTotal"] = order.GrandTotal.ToString("N0");
                return RedirectToAction("OrderConfirmation", new { orderId = order.Id });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra khi đặt hàng: {ex.Message}";
                model.Cart = cart;
                model.User = await _userManager.FindByIdAsync(userId);
                model.ShippingFee = CalculateShippingFee(cart.TotalAmount);
                model.Tax = CalculateTax(cart.TotalAmount);
                return View("Index", model);
            }
        }

        // GET: Checkout/OrderConfirmation
        public IActionResult OrderConfirmation(int orderId)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var order = _orderRepository.GetById(orderId);
            
            if (order == null || order.UserId != userId)
            {
                TempData["ErrorMessage"] = "Không tìm thấy đơn hàng!";
                return RedirectToAction("Index", "Home");
            }

            return View(order);
        }

        // Helper methods
        private decimal CalculateShippingFee(decimal orderValue)
        {
            // Free shipping for orders over 500,000 VND
            if (orderValue >= 500000)
                return 0;
            
            // Standard shipping fee
            return 30000;
        }

        private decimal CalculateTax(decimal orderValue)
        {
            // 10% VAT
            return orderValue * 0.1m;
        }
    }
}
