@using Microsoft.AspNetCore.Identity
@using EbookMVC.Models
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Elearn Admin</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- AdminLTE -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">

    <!-- Custom styles -->
    <style>
        .content-wrapper {
            min-height: calc(100vh - 101px);
        }
    </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                @if (SignInManager.IsSignedIn(User))
                {
                    <li class="nav-item">
                        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                    </li>
                }
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="/" class="nav-link">Trang chủ</a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </a>
                </li>

            </ul>
            <div>
                <partial name="_LoginPartial" />
            </div>
            <div class="d-flex">
                <a href="/Cart" class="btn btn-outline-primary position-relative">
                    <i class="fas fa-shopping-cart"></i>
                    Giỏ hàng
                    <span id="cart-count" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">
                        0
                    </span>
                </a>
                <a href="/Shop" class="btn btn-primary ml-2">
                    <i class="fas fa-store"></i>
                    Cửa hàng
                </a>
            </div>
        </nav>
        <!-- /.navbar -->

        <!-- Main Sidebar Container -->
        @if (SignInManager.IsSignedIn(User))
        {
            <aside class="main-sidebar sidebar-dark-primary elevation-4">
                <!-- Brand Logo -->
                <a href="/" class="brand-link">
                    <i class="fas fa-graduation-cap brand-image elevation-3 ml-3" style="opacity: .8"></i>
                    <span class="brand-text font-weight-light">Elearn Admin</span>
                </a>

                <!-- Sidebar -->
                <div class="sidebar">
                    <!-- Sidebar Menu -->
                    <nav class="mt-2">
                        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                            <li class="nav-item">
                                <a href="/Admin/Home" class="nav-link">
                                    <i class="nav-icon fas fa-tachometer-alt"></i>
                                    <p>Dashboard</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/Admin/Product" class="nav-link">
                                    <i class="nav-icon fas fa-book"></i>
                                    <p>Quản lý khóa học</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/Admin/Order" class="nav-link">
                                    <i class="nav-icon fas fa-shopping-bag"></i>
                                    <p>Quản lý đơn hàng</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/Admin/Category" class="nav-link">
                                    <i class="nav-icon fas fa-list"></i>
                                    <p>Quản lý danh mục</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/Admin/Voucher" class="nav-link">
                                    <i class="nav-icon fas fa-ticket-alt"></i>
                                    <p>Quản lý Voucher</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="/Admin/Home/UserLogs" class="nav-link">
                                    <i class="nav-icon fas fa-history"></i>
                                    <p>Nhật ký hoạt động</p>
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <!-- /.sidebar-menu -->
                </div>
                <!-- /.sidebar -->
            </aside>
        }

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper" style="@(!SignInManager.IsSignedIn(User) ? "margin-left: 0;" : "")">
            <!-- Notification Area -->
            <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1080;">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="toast show bg-success text-white" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header bg-success text-white">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong class="me-auto">Thành công</strong>
                            <small>Vừa xong</small>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            @TempData["SuccessMessage"]
                        </div>
                    </div>
                }
                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="toast show bg-danger text-white" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header bg-danger text-white">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong class="me-auto">Lỗi</strong>
                            <small>Vừa xong</small>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            @TempData["ErrorMessage"]
                        </div>
                    </div>
                }
                @if (TempData["WarningMessage"] != null)
                {
                    <div class="toast show bg-warning" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header bg-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong class="me-auto">Cảnh báo</strong>
                            <small>Vừa xong</small>
                            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            @TempData["WarningMessage"]
                        </div>
                    </div>
                }
                @if (TempData["InfoMessage"] != null)
                {
                    <div class="toast show bg-info text-white" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header bg-info text-white">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong class="me-auto">Thông tin</strong>
                            <small>Vừa xong</small>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            @TempData["InfoMessage"]
                        </div>
                    </div>
                }
            </div>
            <!-- End Notification Area -->

            @RenderBody()
        </div>
        <!-- /.content-wrapper -->

        <footer class="main-footer">
            <div class="float-right d-none d-sm-block">
                <b>Version</b> 1.0.0
            </div>
            <strong>Copyright &copy; 2024 <a href="/">Elearn</a>.</strong> All rights reserved.
        </footer>
    </div>
    <!-- ./wrapper -->

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 5 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

    <!-- Notification Auto-Hide -->
    <script>
        $(document).ready(function() {
            // Tự động ẩn thông báo sau 5 giây
            setTimeout(function() {
                $('.toast').toast('hide');
            }, 5000);

            // Xử lý sự kiện đóng thông báo
            $('.btn-close').on('click', function() {
                $(this).closest('.toast').toast('hide');
            });

            // Cập nhật số lượng giỏ hàng
            updateCartCount();
        });

        function updateCartCount() {
            @if (SignInManager.IsSignedIn(User))
            {
                <text>
                $.ajax({
                    url: '/Cart/GetCartItemCount',
                    type: 'GET',
                    success: function(response) {
                        var count = response.count || 0;
                        var cartBadge = $('#cart-count');
                        if (count > 0) {
                            cartBadge.text(count).show();
                        } else {
                            cartBadge.hide();
                        }
                    },
                    error: function() {
                        console.log('Không thể lấy số lượng giỏ hàng');
                    }
                });
                </text>
            }
        }

        // Cập nhật số lượng giỏ hàng mỗi 30 giây
        setInterval(updateCartCount, 30000);
    </script>
    <!-- add bootstrap để lấy icon -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
