@model EbookMVC.Models.Order

@{
    ViewData["Title"] = "Xác nhận đơn hàng";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-check-circle"></i>
                        Đặt hàng thành công!
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-thumbs-up"></i> Cảm ơn bạn đã đặt hàng!</h5>
                        <p class="mb-2">Đơn hàng của bạn đã được tiếp nhận và đang được xử lý. Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.</p>
                        <p class="mb-0"><strong>Mã đơn hàng:</strong> #@Model.Id.ToString("D6")</p>
                    </div>

                    <!-- Auto redirect notification -->
                    <div class="alert alert-info" id="redirectNotification">
                        <i class="fas fa-info-circle"></i>
                        Trang sẽ tự động chuyển về trang chủ sau <span id="countdown">10</span> giây.
                        <button type="button" class="btn btn-sm btn-outline-info ml-2" onclick="cancelRedirect()">Hủy</button>
                    </div>

                    <!-- Order Information -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="fas fa-info-circle"></i> Thông tin đơn hàng</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Mã đơn hàng:</strong></td>
                                    <td>#@Model.Id.ToString("D6")</td>
                                </tr>
                                <tr>
                                    <td><strong>Ngày đặt:</strong></td>
                                    <td>@Model.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                                </tr>
                                <tr>
                                    <td><strong>Trạng thái:</strong></td>
                                    <td>
                                        <span class="badge badge-warning">Đang xử lý</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Phương thức thanh toán:</strong></td>
                                    <td>@Model.PaymentMethod</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="fas fa-shipping-fast"></i> Thông tin giao hàng</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Địa chỉ:</strong></td>
                                    <td>@Model.ShippingAddress</td>
                                </tr>
                                <tr>
                                    <td><strong>Điện thoại:</strong></td>
                                    <td>@Model.ShippingPhone</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>@Model.ShippingEmail</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.Notes))
                                {
                                    <tr>
                                        <td><strong>Ghi chú:</strong></td>
                                        <td>@Model.Notes</td>
                                    </tr>
                                }
                            </table>
                        </div>
                    </div>

                    <hr>

                    <!-- Order Items -->
                    <h5><i class="fas fa-list"></i> Chi tiết đơn hàng</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="thead-light">
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th class="text-center">Số lượng</th>
                                    <th class="text-right">Đơn giá</th>
                                    <th class="text-right">Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderItems)
                                {
                                    <tr>
                                        <td>@item.ProductName</td>
                                        <td class="text-center">@item.Quantity</td>
                                        <td class="text-right">@item.UnitPrice.ToString("N0") VNĐ</td>
                                        <td class="text-right">@item.TotalPrice.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Order Summary -->
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <table class="table">
                                <tr>
                                    <td><strong>Tạm tính:</strong></td>
                                    <td class="text-right">@Model.TotalAmount.ToString("N0") VNĐ</td>
                                </tr>
                                @if (Model.VoucherDiscount > 0)
                                {
                                    <tr class="text-success">
                                        <td><strong>Giảm giá (@Model.VoucherCode):</strong></td>
                                        <td class="text-right">-@Model.VoucherDiscount.ToString("N0") VNĐ</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Phí vận chuyển:</strong></td>
                                    <td class="text-right">@Model.ShippingFee.ToString("N0") VNĐ</td>
                                </tr>
                                <tr>
                                    <td><strong>Thuế:</strong></td>
                                    <td class="text-right">@Model.Tax.ToString("N0") VNĐ</td>
                                </tr>
                                <tr class="table-active">
                                    <td><strong>Tổng cộng:</strong></td>
                                    <td class="text-right"><strong class="text-primary">@Model.GrandTotal.ToString("N0") VNĐ</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.VoucherCode))
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-ticket-alt"></i>
                            <strong>Voucher đã sử dụng:</strong> @Model.VoucherCode
                            (Tiết kiệm: @Model.VoucherDiscount.ToString("N0") VNĐ)
                        </div>
                    }
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-md-6">
                            <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-home"></i>
                                Về trang chủ
                            </a>
                            <a asp-controller="Order" asp-action="Index" class="btn btn-info">
                                <i class="fas fa-list"></i>
                                Xem đơn hàng của tôi
                            </a>
                        </div>
                        <div class="col-md-6 text-right">
                            <button onclick="printOrder()" class="btn btn-secondary">
                                <i class="fas fa-print"></i>
                                In đơn hàng
                            </button>
                            <button onclick="cancelRedirect()" class="btn btn-info">
                                <i class="fas fa-pause"></i>
                                Dừng chuyển hướng
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let countdownTimer;
        let redirectTimer;
        let timeLeft = 10;

        $(document).ready(function() {
            // Start countdown
            startCountdown();

            // Show success notification
            toastr.success('Đặt hàng thành công! Mã đơn hàng: #@Model.Id.ToString("D6")', 'Thành công', {
                timeOut: 5000,
                progressBar: true
            });
        });

        function startCountdown() {
            countdownTimer = setInterval(function() {
                timeLeft--;
                $('#countdown').text(timeLeft);

                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    window.location.href = '@Url.Action("Index", "Home")';
                }
            }, 1000);
        }

        function cancelRedirect() {
            clearInterval(countdownTimer);
            $('#redirectNotification').fadeOut();
        }

        // Print function
        function printOrder() {
            window.print();
        }
    </script>
}

<style>
    @@media print {
        .card-footer, .btn, #redirectNotification {
            display: none !important;
        }

        .alert-success {
            border: 1px solid #d4edda !important;
            background-color: #d4edda !important;
            color: #155724 !important;
        }
    }

    .alert-success {
        animation: slideInDown 0.5s ease-out;
    }

    @@keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>
