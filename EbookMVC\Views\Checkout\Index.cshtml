@model EbookMVC.Models.CheckoutViewModel

@{
    ViewData["Title"] = "Thanh toán";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-credit-card"></i>
                        Thông tin thanh toán
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="PlaceOrder" method="post" id="checkoutForm">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <!-- Shipping Address -->
                        <div class="row">
                            <div class="col-12">
                                <h5><i class="fas fa-shipping-fast"></i> Thông tin giao hàng</h5>
                                <hr>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ShippingAddress.FullName" class="control-label"></label>
                                    <input asp-for="ShippingAddress.FullName" class="form-control" />
                                    <span asp-validation-for="ShippingAddress.FullName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ShippingAddress.Email" class="control-label"></label>
                                    <input asp-for="ShippingAddress.Email" class="form-control" />
                                    <span asp-validation-for="ShippingAddress.Email" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ShippingAddress.PhoneNumber" class="control-label"></label>
                                    <input asp-for="ShippingAddress.PhoneNumber" class="form-control" />
                                    <span asp-validation-for="ShippingAddress.PhoneNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ShippingAddress.City" class="control-label"></label>
                                    <input asp-for="ShippingAddress.City" class="form-control" />
                                    <span asp-validation-for="ShippingAddress.City" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label asp-for="ShippingAddress.Address" class="control-label"></label>
                                    <input asp-for="ShippingAddress.Address" class="form-control" />
                                    <span asp-validation-for="ShippingAddress.Address" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="ShippingAddress.PostalCode" class="control-label"></label>
                                    <input asp-for="ShippingAddress.PostalCode" class="form-control" />
                                    <span asp-validation-for="ShippingAddress.PostalCode" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="row">
                            <div class="col-12">
                                <h5><i class="fas fa-money-bill-wave"></i> Phương thức thanh toán</h5>
                                <hr>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="COD" id="cod" checked>
                                        <label class="form-check-label" for="cod">
                                            <i class="fas fa-money-bill"></i> Thanh toán khi nhận hàng (COD)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="BankTransfer" id="bank">
                                        <label class="form-check-label" for="bank">
                                            <i class="fas fa-university"></i> Chuyển khoản ngân hàng
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="CreditCard" id="card">
                                        <label class="form-check-label" for="card">
                                            <i class="fas fa-credit-card"></i> Thẻ tín dụng/ghi nợ
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label asp-for="Notes" class="control-label"></label>
                                    <textarea asp-for="Notes" class="form-control" rows="3" placeholder="Ghi chú thêm cho đơn hàng (tùy chọn)"></textarea>
                                    <span asp-validation-for="Notes" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <input asp-for="ShippingFee" type="hidden" />
                        <input asp-for="Tax" type="hidden" />
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Order Summary -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-shopping-cart"></i>
                        Tóm tắt đơn hàng
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Cart Items -->
                    @if (Model.Cart?.CartItems?.Any() == true)
                    {
                        @foreach (var item in Model.Cart.CartItems)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">@item.Product.Name</h6>
                                    <small class="text-muted">@item.Quantity x @item.UnitPrice.ToString("N0") VNĐ</small>
                                </div>
                                <div>
                                    <strong>@item.TotalPrice.ToString("N0") VNĐ</strong>
                                </div>
                            </div>
                        }
                        <hr>
                    }

                    <!-- Voucher Section -->
                    <div class="voucher-section mb-3">
                        <div class="input-group">
                            <input type="text" class="form-control" id="voucherCode" placeholder="Nhập mã voucher" 
                                   value="@Model.VoucherCode">
                            <div class="input-group-append">
                                <button class="btn btn-outline-primary" type="button" id="applyVoucherBtn">
                                    Áp dụng
                                </button>
                            </div>
                        </div>
                        <div id="voucherMessage" class="mt-2"></div>
                        
                        @if (Model.HasVoucher)
                        {
                            <div class="alert alert-success mt-2" id="voucherApplied">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-check-circle"></i>
                                        <strong>@Model.VoucherCode</strong> đã được áp dụng
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="removeVoucherBtn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Order Totals -->
                    <div class="order-totals">
                        <div class="d-flex justify-content-between">
                            <span>Tạm tính:</span>
                            <span id="subtotal">@Model.SubTotal.ToString("N0") VNĐ</span>
                        </div>
                        
                        @if (Model.HasVoucher)
                        {
                            <div class="d-flex justify-content-between text-success" id="voucherDiscountRow">
                                <span>Giảm giá:</span>
                                <span id="voucherDiscount">-@Model.VoucherDiscount.ToString("N0") VNĐ</span>
                            </div>
                        }
                        
                        <div class="d-flex justify-content-between">
                            <span>Phí vận chuyển:</span>
                            <span id="shippingFee">@Model.ShippingFee.ToString("N0") VNĐ</span>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <span>Thuế:</span>
                            <span id="tax">@Model.Tax.ToString("N0") VNĐ</span>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <strong>Tổng cộng:</strong>
                            <strong class="text-primary" id="grandTotal">@Model.GrandTotal.ToString("N0") VNĐ</strong>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" form="checkoutForm" class="btn btn-primary btn-block btn-lg" id="placeOrderBtn">
                        <i class="fas fa-check"></i>
                        Đặt hàng
                    </button>
                    <a asp-controller="Cart" asp-action="Index" class="btn btn-secondary btn-block mt-2">
                        <i class="fas fa-arrow-left"></i>
                        Quay lại giỏ hàng
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Apply voucher
            $('#applyVoucherBtn').click(function() {
                var voucherCode = $('#voucherCode').val().trim();
                if (!voucherCode) {
                    showVoucherMessage('Vui lòng nhập mã voucher!', 'danger');
                    return;
                }

                applyVoucher(voucherCode);
            });

            // Remove voucher
            $('#removeVoucherBtn').click(function() {
                removeVoucher();
            });

            // Enter key to apply voucher
            $('#voucherCode').keypress(function(e) {
                if (e.which == 13) {
                    $('#applyVoucherBtn').click();
                }
            });

            // Handle form submission
            $('#checkoutForm').on('submit', function(e) {
                var btn = $('#placeOrderBtn');
                btn.prop('disabled', true);
                btn.html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');

                // Show processing message
                toastr.info('Đang xử lý đơn hàng của bạn...', 'Vui lòng chờ');
            });
        });
        
        function applyVoucher(voucherCode) {
            $.ajax({
                url: '@Url.Action("ApplyVoucher", "Checkout")',
                type: 'POST',
                data: { voucherCode: voucherCode },
                beforeSend: function() {
                    $('#applyVoucherBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...');
                },
                success: function(response) {
                    if (response.success) {
                        showVoucherMessage(response.message, 'success');
                        updateOrderTotals(response.data);
                        showVoucherApplied(voucherCode);
                    } else {
                        showVoucherMessage(response.message, 'danger');
                    }
                },
                error: function() {
                    showVoucherMessage('Có lỗi xảy ra khi áp dụng voucher!', 'danger');
                },
                complete: function() {
                    $('#applyVoucherBtn').prop('disabled', false).html('Áp dụng');
                }
            });
        }
        
        function removeVoucher() {
            $.ajax({
                url: '@Url.Action("RemoveVoucher", "Checkout")',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        showVoucherMessage(response.message, 'info');
                        updateOrderTotals(response.data);
                        hideVoucherApplied();
                        $('#voucherCode').val('');
                    } else {
                        showVoucherMessage(response.message, 'danger');
                    }
                },
                error: function() {
                    showVoucherMessage('Có lỗi xảy ra khi hủy voucher!', 'danger');
                }
            });
        }
        
        function showVoucherMessage(message, type) {
            $('#voucherMessage').html('<div class="alert alert-' + type + ' alert-dismissible fade show">' +
                '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                message + '</div>');
        }
        
        function updateOrderTotals(data) {
            $('#subtotal').text(formatCurrency(data.orderValue));
            $('#shippingFee').text(formatCurrency(data.shippingFee));
            $('#tax').text(formatCurrency(data.tax));
            $('#grandTotal').text(formatCurrency(data.finalAmount));
            
            if (data.discount && data.discount > 0) {
                if ($('#voucherDiscountRow').length === 0) {
                    $('#subtotal').parent().after('<div class="d-flex justify-content-between text-success" id="voucherDiscountRow"><span>Giảm giá:</span><span id="voucherDiscount">-' + formatCurrency(data.discount) + '</span></div>');
                } else {
                    $('#voucherDiscount').text('-' + formatCurrency(data.discount));
                }
            } else {
                $('#voucherDiscountRow').remove();
            }
        }
        
        function showVoucherApplied(voucherCode) {
            var html = '<div class="alert alert-success mt-2" id="voucherApplied">' +
                '<div class="d-flex justify-content-between align-items-center">' +
                '<div><i class="fas fa-check-circle"></i> <strong>' + voucherCode + '</strong> đã được áp dụng</div>' +
                '<button type="button" class="btn btn-sm btn-outline-danger" id="removeVoucherBtn"><i class="fas fa-times"></i></button>' +
                '</div></div>';
            
            if ($('#voucherApplied').length === 0) {
                $('.voucher-section').append(html);
            }
            
            // Re-bind remove voucher event
            $('#removeVoucherBtn').click(function() {
                removeVoucher();
            });
        }
        
        function hideVoucherApplied() {
            $('#voucherApplied').remove();
        }
        
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN').format(amount) + ' VNĐ';
        }
    </script>
}
