using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using EbookMVC.Models;
using EbookMVC.Repository;
using EbookMVC.Areas.Admin.Models;

namespace EbookMVC.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = SD.Role_Admin)]
    public class VoucherController : Controller
    {
        private readonly IVoucherRepository _voucherRepository;
        private readonly IUserLogRepository _userLogRepository;

        public VoucherController(
            IVoucherRepository voucherRepository,
            IUserLogRepository userLogRepository)
        {
            _voucherRepository = voucherRepository;
            _userLogRepository = userLogRepository;
        }

        // GET: Admin/Voucher
        public IActionResult Index(string searchKeyword, VoucherType? type, bool? isActive)
        {
            IEnumerable<Voucher> vouchers;

            if (!string.IsNullOrEmpty(searchKeyword))
            {
                vouchers = _voucherRepository.Search(searchKeyword);
            }
            else
            {
                vouchers = _voucherRepository.GetAll();
            }

            // Filter by type
            if (type.HasValue)
            {
                vouchers = vouchers.Where(v => v.Type == type.Value);
            }

            // Filter by active status
            if (isActive.HasValue)
            {
                vouchers = vouchers.Where(v => v.IsActive == isActive.Value);
            }

            ViewBag.SearchKeyword = searchKeyword;
            ViewBag.Type = type;
            ViewBag.IsActive = isActive;
            ViewBag.VoucherTypes = Enum.GetValues<VoucherType>();

            return View(vouchers);
        }

        // GET: Admin/Voucher/Details/5
        public IActionResult Details(int id)
        {
            try
            {
                var voucher = _voucherRepository.GetById(id);
                return View(voucher);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // GET: Admin/Voucher/Create
        public IActionResult Create()
        {
            var voucher = new Voucher
            {
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(30),
                IsActive = true
            };
            
            ViewBag.VoucherTypes = Enum.GetValues<VoucherType>();
            return View(voucher);
        }

        // POST: Admin/Voucher/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Create(Voucher voucher)
        {
            // Validate voucher code uniqueness
            if (_voucherRepository.IsCodeExists(voucher.Code))
            {
                ModelState.AddModelError("Code", "Mã voucher đã tồn tại!");
            }

            // Validate dates
            if (voucher.EndDate <= voucher.StartDate)
            {
                ModelState.AddModelError("EndDate", "Ngày kết thúc phải sau ngày bắt đầu!");
            }

            // Validate percentage value
            if (voucher.Type == VoucherType.Percentage && voucher.Value > 100)
            {
                ModelState.AddModelError("Value", "Giá trị phần trăm không được vượt quá 100%!");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    voucher.CreatedBy = User.Identity?.Name ?? "Admin";
                    voucher.UpdatedBy = User.Identity?.Name ?? "Admin";
                    voucher.CreatedDate = DateTime.Now;
                    voucher.UpdatedDate = DateTime.Now;

                    _voucherRepository.Add(voucher);

                    // Ghi log
                    _userLogRepository.Add(new UserLog
                    {
                        Action = "CREATE",
                        EntityName = "Voucher",
                        EntityId = voucher.Id,
                        Description = $"Tạo voucher mới: {voucher.Code} - {voucher.Name}",
                        IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                        UserName = User.Identity?.Name ?? "Admin",
                        Timestamp = DateTime.Now
                    });

                    TempData["SuccessMessage"] = "Thêm voucher thành công!";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = $"Có lỗi xảy ra khi lưu voucher: {ex.Message}";
                }
            }

            ViewBag.VoucherTypes = Enum.GetValues<VoucherType>();
            return View(voucher);
        }

        // GET: Admin/Voucher/Edit/5
        public IActionResult Edit(int id)
        {
            try
            {
                var voucher = _voucherRepository.GetById(id);
                ViewBag.VoucherTypes = Enum.GetValues<VoucherType>();
                return View(voucher);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // POST: Admin/Voucher/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(int id, Voucher voucher)
        {
            if (id != voucher.Id)
            {
                TempData["ErrorMessage"] = "ID voucher không hợp lệ!";
                return RedirectToAction(nameof(Index));
            }

            // Validate voucher code uniqueness (excluding current voucher)
            if (_voucherRepository.IsCodeExists(voucher.Code, voucher.Id))
            {
                ModelState.AddModelError("Code", "Mã voucher đã tồn tại!");
            }

            // Validate dates
            if (voucher.EndDate <= voucher.StartDate)
            {
                ModelState.AddModelError("EndDate", "Ngày kết thúc phải sau ngày bắt đầu!");
            }

            // Validate percentage value
            if (voucher.Type == VoucherType.Percentage && voucher.Value > 100)
            {
                ModelState.AddModelError("Value", "Giá trị phần trăm không được vượt quá 100%!");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    voucher.UpdatedBy = User.Identity?.Name ?? "Admin";
                    voucher.UpdatedDate = DateTime.Now;

                    _voucherRepository.Update(voucher);

                    // Ghi log
                    _userLogRepository.Add(new UserLog
                    {
                        Action = "UPDATE",
                        EntityName = "Voucher",
                        EntityId = voucher.Id,
                        Description = $"Cập nhật voucher: {voucher.Code} - {voucher.Name}",
                        IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                        UserName = User.Identity?.Name ?? "Admin",
                        Timestamp = DateTime.Now
                    });

                    TempData["SuccessMessage"] = "Cập nhật voucher thành công!";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = $"Có lỗi xảy ra khi cập nhật voucher: {ex.Message}";
                }
            }

            ViewBag.VoucherTypes = Enum.GetValues<VoucherType>();
            return View(voucher);
        }

        // GET: Admin/Voucher/Delete/5
        public IActionResult Delete(int id)
        {
            try
            {
                var voucher = _voucherRepository.GetById(id);
                return View(voucher);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // POST: Admin/Voucher/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeleteConfirmed(int id)
        {
            try
            {
                var voucher = _voucherRepository.GetById(id);
                _voucherRepository.Delete(id);

                // Ghi log
                _userLogRepository.Add(new UserLog
                {
                    Action = "DELETE",
                    EntityName = "Voucher",
                    EntityId = id,
                    Description = $"Xóa voucher: {voucher.Code} - {voucher.Name}",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Admin",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = "Xóa voucher thành công!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra khi xóa voucher: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: Admin/Voucher/ToggleStatus/5
        [HttpPost]
        public IActionResult ToggleStatus(int id)
        {
            try
            {
                var voucher = _voucherRepository.GetById(id);
                voucher.IsActive = !voucher.IsActive;
                voucher.UpdatedBy = User.Identity?.Name ?? "Admin";
                voucher.UpdatedDate = DateTime.Now;

                _voucherRepository.Update(voucher);

                // Ghi log
                _userLogRepository.Add(new UserLog
                {
                    Action = "UPDATE_STATUS",
                    EntityName = "Voucher",
                    EntityId = id,
                    Description = $"Thay đổi trạng thái voucher {voucher.Code} thành {voucher.StatusDisplay}",
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                    UserName = User.Identity?.Name ?? "Admin",
                    Timestamp = DateTime.Now
                });

                TempData["SuccessMessage"] = $"Đã thay đổi trạng thái voucher thành {voucher.StatusDisplay}!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra: {ex.Message}";
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: Admin/Voucher/CreateSampleData - Tạo dữ liệu mẫu để test
        public IActionResult CreateSampleData()
        {
            try
            {
                // Kiểm tra xem đã có voucher nào chưa
                if (_voucherRepository.GetAll().Any())
                {
                    TempData["ErrorMessage"] = "Đã có dữ liệu voucher trong hệ thống!";
                    return RedirectToAction("Index");
                }

                var sampleVouchers = new List<Voucher>
                {
                    new Voucher
                    {
                        Code = "WELCOME10",
                        Name = "Chào mừng khách hàng mới",
                        Description = "Giảm 10% cho đơn hàng đầu tiên",
                        Type = VoucherType.Percentage,
                        Value = 10,
                        MinOrderValue = 500000,
                        MaxDiscount = 100000,
                        StartDate = DateTime.Now,
                        EndDate = DateTime.Now.AddDays(30),
                        Quantity = 100,
                        IsActive = true,
                        CreatedBy = "System",
                        UpdatedBy = "System"
                    },
                    new Voucher
                    {
                        Code = "SALE50K",
                        Name = "Giảm 50K cho đơn từ 1 triệu",
                        Description = "Giảm 50,000 VNĐ cho đơn hàng từ 1,000,000 VNĐ",
                        Type = VoucherType.FixedAmount,
                        Value = 50000,
                        MinOrderValue = 1000000,
                        StartDate = DateTime.Now,
                        EndDate = DateTime.Now.AddDays(15),
                        Quantity = 50,
                        IsActive = true,
                        CreatedBy = "System",
                        UpdatedBy = "System"
                    },
                    new Voucher
                    {
                        Code = "FREESHIP",
                        Name = "Miễn phí vận chuyển",
                        Description = "Miễn phí vận chuyển cho đơn hàng từ 300,000 VNĐ",
                        Type = VoucherType.FixedAmount,
                        Value = 30000,
                        MinOrderValue = 300000,
                        StartDate = DateTime.Now,
                        EndDate = DateTime.Now.AddDays(60),
                        IsActive = true,
                        CreatedBy = "System",
                        UpdatedBy = "System"
                    },
                    new Voucher
                    {
                        Code = "BIGDEAL20",
                        Name = "Ưu đãi lớn 20%",
                        Description = "Giảm 20% tối đa 200,000 VNĐ cho đơn hàng từ 2,000,000 VNĐ",
                        Type = VoucherType.Percentage,
                        Value = 20,
                        MinOrderValue = 2000000,
                        MaxDiscount = 200000,
                        StartDate = DateTime.Now,
                        EndDate = DateTime.Now.AddDays(7),
                        Quantity = 20,
                        IsActive = true,
                        CreatedBy = "System",
                        UpdatedBy = "System"
                    }
                };

                foreach (var voucher in sampleVouchers)
                {
                    _voucherRepository.Add(voucher);

                    // Ghi log
                    _userLogRepository.Add(new UserLog
                    {
                        Action = "CREATE_SAMPLE",
                        EntityName = "Voucher",
                        EntityId = voucher.Id,
                        Description = $"Tạo voucher mẫu: {voucher.Code} - {voucher.Name}",
                        IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "127.0.0.1",
                        UserName = User.Identity?.Name ?? "System",
                        Timestamp = DateTime.Now
                    });
                }

                TempData["SuccessMessage"] = $"Đã tạo {sampleVouchers.Count} voucher mẫu thành công!";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Có lỗi xảy ra khi tạo dữ liệu mẫu: {ex.Message}";
                return RedirectToAction("Index");
            }
        }
    }
}
